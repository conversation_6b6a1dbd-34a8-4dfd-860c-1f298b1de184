@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for professional invoice table */
.invoice-table-row {
  @apply transition-all duration-200 ease-in-out;
}

.invoice-table-row:hover {
  @apply bg-gray-50/80 shadow-sm;
}

.invoice-input {
  @apply transition-all duration-150 ease-in-out;
}

.invoice-input:focus {
  @apply ring-2 ring-gray-900/20 border-gray-900 shadow-sm;
}

.invoice-total-display {
  @apply bg-gradient-to-r from-gray-50 to-gray-100 border-2 border-gray-200;
}

/* Smooth animations for add/remove items */
.invoice-item-enter {
  @apply opacity-0 transform scale-95;
}

.invoice-item-enter-active {
  @apply opacity-100 transform scale-100 transition-all duration-300 ease-out;
}

.invoice-item-exit {
  @apply opacity-100 transform scale-100;
}

.invoice-item-exit-active {
  @apply opacity-0 transform scale-95 transition-all duration-200 ease-in;
}
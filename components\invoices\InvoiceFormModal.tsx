import React, { useState, useEffect, useMemo } from 'react';
import { Invoice, InvoiceItem, InvoiceStatus, Client, Product, TaxRate } from '../../types';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Select from '../ui/Select';
import ProductSelector from '../ui/ProductSelector';

interface InvoiceFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (invoice: Omit<Invoice, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>) => void;
  invoice: Invoice | null;
  clients: Client[];
  products: Product[];
  taxRates: TaxRate[];
}

interface FormErrors {
  clientId?: string;
  issueDate?: string;
  dueDate?: string;
  items?: string[];
  general?: string;
}

const today = new Date().toISOString().split('T')[0];
const defaultDueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 30 days from today

const emptyItem: InvoiceItem = { description: '', quantity: 1, unitPrice: 0, total: 0 };

// Generate invoice number
const generateInvoiceNumber = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `INV-${year}${month}-${random}`;
};

const InvoiceFormModal: React.FC<InvoiceFormModalProps> = ({ isOpen, onClose, onSave, invoice, clients, products, taxRates }) => {

  const newInvoiceTemplate = useMemo(() => {
    const defaultTax = taxRates.find(t => t.isDefault);
    return {
      clientId: '',
      invoiceNumber: generateInvoiceNumber(),
      issueDate: today,
      dueDate: defaultDueDate,
      status: InvoiceStatus.Draft,
      items: [emptyItem],
      taxId: defaultTax?._id,
      notes: '',
      discountType: 'percentage' as 'percentage' | 'fixed',
      discountValue: 0,
    }
  }, [taxRates]);

  const [formData, setFormData] = useState<Omit<Invoice, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>>({ ...newInvoiceTemplate });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = useMemo(() => !!invoice, [invoice]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      setIsSubmitting(false);
      if (invoice) {
        const { _id, _creationTime, workspaceId, amount, taxRate, ...editableData } = invoice;
        setFormData({ ...editableData });
      } else {
        setFormData({ ...newInvoiceTemplate, items: [{ ...emptyItem }] });
      }
    }
  }, [invoice, isOpen, newInvoiceTemplate]);

  // Validation functions
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.clientId) {
      newErrors.clientId = 'Please select a client';
    }

    if (!formData.issueDate) {
      newErrors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required';
    } else if (new Date(formData.dueDate) < new Date(formData.issueDate)) {
      newErrors.dueDate = 'Due date cannot be before issue date';
    }

    // Validate items
    const itemErrors: string[] = [];
    formData.items.forEach((item, index) => {
      if (!item.description.trim()) {
        itemErrors[index] = 'Description is required';
      } else if (item.quantity <= 0) {
        itemErrors[index] = 'Quantity must be greater than 0';
      } else if (item.unitPrice < 0) {
        itemErrors[index] = 'Unit price cannot be negative';
      }
    });

    if (itemErrors.some(error => error)) {
      newErrors.items = itemErrors;
    }

    if (formData.items.length === 0) {
      newErrors.general = 'At least one item is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear specific field error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const items = [...formData.items];
    const item = { ...items[index] };

    if (name === 'description') {
      item.description = value;
    } else if (name === 'quantity') {
      item.quantity = Math.max(0, parseFloat(value) || 0);
    } else if (name === 'unitPrice') {
      item.unitPrice = Math.max(0, parseFloat(value) || 0);
    }

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));

    // Clear item-specific errors
    if (errors.items && errors.items[index]) {
      const newItemErrors = [...(errors.items || [])];
      newItemErrors[index] = '';
      setErrors(prev => ({ ...prev, items: newItemErrors }));
    }
  };

  const handleProductSelect = (index: number, description: string, product?: Product) => {
    const items = [...formData.items];
    const item = { ...items[index] };

    item.description = description;
    if (product) {
      item.unitPrice = product.unitPrice;
      item.productId = product._id;
    } else {
      item.productId = undefined;
    }

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));
  };

  const addItem = () => {
    setFormData(prev => ({ ...prev, items: [...prev.items, { ...emptyItem }] }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length <= 1) return;
    const items = formData.items.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, items }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      setErrors({ general: 'Failed to save invoice. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);

  // Calculate discount
  const discountAmount = formData.discountType === 'percentage'
    ? subtotal * (formData.discountValue / 100)
    : formData.discountValue;

  const discountedSubtotal = subtotal - discountAmount;

  // Calculate tax on discounted amount
  const selectedTaxRate = taxRates.find(t => t._id === formData.taxId)?.rate || 0;
  const taxAmount = discountedSubtotal * selectedTaxRate;
  const totalAmount = discountedSubtotal + taxAmount;
  
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={isEditing ? 'Edit Invoice' : 'Create New Invoice'} size="3xl">
      <form onSubmit={handleSubmit} className="h-full flex flex-col">
        <div className="flex-1 overflow-y-auto">
          <div className="p-6 space-y-8">
            {/* Header Section */}
            <div className="bg-gray-50 -m-6 p-6 border-b">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {isEditing ? 'Edit Invoice' : 'Create New Invoice'}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">
                    {isEditing ? 'Update invoice details' : 'Fill in the details below to create a new invoice'}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-500">Invoice Number</div>
                  <div className="text-lg font-bold text-gray-900">{formData.invoiceNumber}</div>
                </div>
              </div>
            </div>

            {/* General Error */}
            {errors.general && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{errors.general}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-6">
              <div>
                <h4 className="text-base font-semibold text-gray-900 mb-4">Basic Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Select
                      label="Customer *"
                      name="clientId"
                      value={formData.clientId}
                      onChange={handleChange}
                      required
                      className={errors.clientId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                    >
                      <option value="" disabled>Select a client</option>
                      {clients.map(client => (
                        <option key={client._id} value={client._id}>{client.name}</option>
                      ))}
                    </Select>
                    {errors.clientId && <p className="mt-1 text-sm text-red-600">{errors.clientId}</p>}
                  </div>

                  <div>
                    <Select label="Status" name="status" value={formData.status} onChange={handleChange}>
                      {Object.values(InvoiceStatus).map(s => (
                        <option key={s} value={s}>{s}</option>
                      ))}
                    </Select>
                  </div>

                  <div>
                    <Input
                      label="Issue Date *"
                      type="date"
                      name="issueDate"
                      value={formData.issueDate}
                      onChange={handleChange}
                      required
                      className={errors.issueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                    />
                    {errors.issueDate && <p className="mt-1 text-sm text-red-600">{errors.issueDate}</p>}
                  </div>

                  <div>
                    <Input
                      label="Due Date *"
                      type="date"
                      name="dueDate"
                      value={formData.dueDate}
                      onChange={handleChange}
                      required
                      className={errors.dueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
                    />
                    {errors.dueDate && <p className="mt-1 text-sm text-red-600">{errors.dueDate}</p>}
                  </div>
                </div>
              </div>

              {/* Items Section */}
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h4 className="text-base font-semibold text-gray-900">Invoice Items</h4>
                  <Button
                    type="button"
                    variant="primary"
                    size="sm"
                    onClick={addItem}
                    className="flex items-center gap-2 bg-gray-900 hover:bg-gray-800"
                  >
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Item
                  </Button>
                </div>

                <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                  {/* Table Header */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                    <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700 uppercase tracking-wide">
                      <div className="col-span-5">Product/Description</div>
                      <div className="col-span-2 text-center">Qty</div>
                      <div className="col-span-2 text-center">Unit Price</div>
                      <div className="col-span-2 text-center">Total</div>
                      <div className="col-span-1 text-center">Actions</div>
                    </div>
                  </div>

                  {/* Table Body */}
                  <div className="divide-y divide-gray-100">
                    {formData.items.map((item, index) => (
                      <div key={index} className="invoice-table-row group">
                        <div className="px-6 py-5">
                          <div className="grid grid-cols-12 gap-4 items-center">
                            {/* Product/Description Column */}
                            <div className="col-span-5">
                              <div className="space-y-2">
                                <ProductSelector
                                  value={item.description}
                                  onChange={(value, product) => handleProductSelect(index, value, product)}
                                  products={products}
                                  placeholder="Search products or enter description..."
                                />
                                {errors.items && errors.items[index] && (
                                  <div className="flex items-center gap-1 text-red-600">
                                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p className="text-sm">{errors.items[index]}</p>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Quantity Column */}
                            <div className="col-span-2">
                              <div className="relative">
                                <input
                                  type="number"
                                  name="quantity"
                                  value={item.quantity}
                                  onChange={(e) => handleItemChange(index, e)}
                                  min="0"
                                  step="1"
                                  placeholder="0"
                                  className="invoice-input w-full px-3 py-2.5 text-center border border-gray-300 rounded-lg shadow-sm font-medium"
                                />
                              </div>
                            </div>

                            {/* Unit Price Column */}
                            <div className="col-span-2">
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <span className="text-gray-500 text-sm">$</span>
                                </div>
                                <input
                                  type="number"
                                  name="unitPrice"
                                  value={item.unitPrice}
                                  onChange={(e) => handleItemChange(index, e)}
                                  min="0"
                                  step="0.01"
                                  placeholder="0.00"
                                  className="invoice-input w-full pl-7 pr-3 py-2.5 text-center border border-gray-300 rounded-lg shadow-sm font-medium"
                                />
                              </div>
                            </div>

                            {/* Total Column */}
                            <div className="col-span-2">
                              <div className="invoice-total-display rounded-lg px-3 py-2.5 text-center">
                                <span className="font-semibold text-gray-900 text-lg">
                                  {formatCurrency(item.total)}
                                </span>
                              </div>
                            </div>

                            {/* Actions Column */}
                            <div className="col-span-1 flex justify-center">
                              <button
                                type="button"
                                onClick={() => removeItem(index)}
                                disabled={formData.items.length <= 1}
                                className="p-2 rounded-lg text-gray-400 hover:text-red-600 hover:bg-red-50 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed group-hover:opacity-100"
                                title="Remove item"
                              >
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Empty State */}
                  {formData.items.length === 0 && (
                    <div className="px-6 py-12 text-center">
                      <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No items added yet</h3>
                      <p className="text-gray-500 mb-4">Add products or services to your invoice</p>
                      <Button
                        type="button"
                        variant="primary"
                        onClick={addItem}
                        className="bg-gray-900 hover:bg-gray-800"
                      >
                        Add Your First Item
                      </Button>
                    </div>
                  )}

                  {/* Table Footer with Item Count */}
                  {formData.items.length > 0 && (
                    <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
                      <div className="flex justify-between items-center text-sm text-gray-600">
                        <span>{formData.items.length} item{formData.items.length !== 1 ? 's' : ''} added</span>
                        <span>Subtotal: <span className="font-semibold text-gray-900">{formatCurrency(subtotal)}</span></span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {/* Notes Section */}
              <div>
                <h4 className="text-base font-semibold text-gray-900 mb-4">Additional Information</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes || ''}
                    onChange={handleChange}
                    rows={3}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-900 focus:border-gray-900 sm:text-sm"
                    placeholder="Add any additional notes or terms for this invoice..."
                  />
                </div>
              </div>

              {/* Totals Section */}
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 -mx-6 px-6 py-8 border-t">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Discount Controls */}
                  <div className="space-y-4">
                    <h5 className="text-base font-semibold text-gray-900 mb-4">Discount & Tax Settings</h5>

                    {/* Discount Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Discount Type</label>
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="discountType"
                            value="percentage"
                            checked={formData.discountType === 'percentage'}
                            onChange={handleChange}
                            className="h-4 w-4 text-gray-900 focus:ring-gray-900 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">Percentage (%)</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="discountType"
                            value="fixed"
                            checked={formData.discountType === 'fixed'}
                            onChange={handleChange}
                            className="h-4 w-4 text-gray-900 focus:ring-gray-900 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">Fixed Amount ($)</span>
                        </label>
                      </div>
                    </div>

                    {/* Discount Value */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Discount {formData.discountType === 'percentage' ? 'Percentage' : 'Amount'}
                      </label>
                      <div className="relative">
                        {formData.discountType === 'fixed' && (
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 text-sm">$</span>
                          </div>
                        )}
                        <input
                          type="number"
                          name="discountValue"
                          value={formData.discountValue}
                          onChange={handleChange}
                          min="0"
                          max={formData.discountType === 'percentage' ? 100 : undefined}
                          step={formData.discountType === 'percentage' ? 1 : 0.01}
                          placeholder="0"
                          className={`invoice-input w-full ${formData.discountType === 'fixed' ? 'pl-7' : ''} pr-3 py-2.5 border border-gray-300 rounded-lg shadow-sm font-medium`}
                        />
                        {formData.discountType === 'percentage' && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 text-sm">%</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Tax Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Tax Rate</label>
                      <Select
                        name="taxId"
                        value={formData.taxId}
                        onChange={handleChange}
                        className="w-full"
                      >
                        {taxRates.map(t => (
                          <option key={t._id} value={t._id}>
                            {t.name} ({(t.rate * 100).toFixed(2)}%)
                          </option>
                        ))}
                      </Select>
                    </div>
                  </div>

                  {/* Totals Display */}
                  <div className="space-y-4">
                    <h5 className="text-base font-semibold text-gray-900 mb-4">Invoice Summary</h5>

                    <div className="bg-white rounded-lg border border-gray-200 p-6 space-y-4 shadow-sm">
                      {/* Subtotal */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600">Subtotal</span>
                        <span className="text-lg font-medium text-gray-900">{formatCurrency(subtotal)}</span>
                      </div>

                      {/* Discount */}
                      {discountAmount > 0 && (
                        <div className="flex justify-between items-center text-green-600">
                          <span className="text-sm font-medium">
                            Discount {formData.discountType === 'percentage' ? `(${formData.discountValue}%)` : ''}
                          </span>
                          <span className="text-lg font-medium">-{formatCurrency(discountAmount)}</span>
                        </div>
                      )}

                      {/* Discounted Subtotal */}
                      {discountAmount > 0 && (
                        <div className="flex justify-between items-center border-t pt-2">
                          <span className="text-sm font-medium text-gray-600">Subtotal after discount</span>
                          <span className="text-lg font-medium text-gray-900">{formatCurrency(discountedSubtotal)}</span>
                        </div>
                      )}

                      {/* Tax */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600">
                          {taxRates.find(t => t._id === formData.taxId)?.name || 'Tax'} ({(selectedTaxRate * 100).toFixed(2)}%)
                        </span>
                        <span className="text-lg font-medium text-gray-900">{formatCurrency(taxAmount)}</span>
                      </div>

                      {/* Total */}
                      <div className="border-t border-gray-200 pt-4">
                        <div className="flex justify-between items-center">
                          <span className="text-xl font-bold text-gray-900">Total Amount</span>
                          <span className="text-2xl font-bold text-gray-900">{formatCurrency(totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Footer */}
        <div className="flex-shrink-0 px-6 py-4 bg-white border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {isEditing ? 'Update invoice details' : 'All fields marked with * are required'}
            </div>
            <div className="flex space-x-3">
              <Button
                type="button"
                variant="secondary"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </div>
                ) : (
                  isEditing ? 'Update Invoice' : 'Create Invoice'
                )}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default InvoiceFormModal;
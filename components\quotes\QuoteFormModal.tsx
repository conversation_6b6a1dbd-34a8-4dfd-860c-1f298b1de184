import React, { useState, useEffect, useMemo } from 'react';
import { Quote, InvoiceItem, QuoteStatus, Client, Product, TaxRate } from '../../types';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Select from '../ui/Select';
import ProductSelector from '../ui/ProductSelector';

interface QuoteFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (quote: Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>) => void;
  quote: Quote | null;
  clients: Client[];
  products: Product[];
  taxRates: TaxRate[];
}

interface FormErrors {
  clientId?: string;
  issueDate?: string;
  expiryDate?: string;
  items?: string[];
  general?: string;
}

const today = new Date().toISOString().split('T')[0];
const futureDate = new Date();
futureDate.setDate(futureDate.getDate() + 30);
const expiry = futureDate.toISOString().split('T')[0];

const emptyItem: InvoiceItem = { description: '', quantity: 1, unitPrice: 0, total: 0 };

const generateQuoteNumber = () => `QUO-${Date.now().toString().slice(-6)}`;

const QuoteFormModal: React.FC<QuoteFormModalProps> = ({ isOpen, onClose, onSave, quote, clients, products, taxRates }) => {

  const newQuoteTemplate = useMemo(() => {
    const defaultTax = taxRates.find(t => t.isDefault);
    return {
      clientId: '',
      quoteNumber: generateQuoteNumber(),
      issueDate: today,
      expiryDate: expiry,
      status: QuoteStatus.Draft,
      items: [emptyItem],
      taxId: defaultTax?._id,
    }
  }, [taxRates]);

  const [formData, setFormData] = useState<Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>>({ ...newQuoteTemplate });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = useMemo(() => !!quote, [quote]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      setIsSubmitting(false);
      if (quote) {
        const { _id, amount, workspaceId, _creationTime, taxRate, ...editableData } = quote;
        setFormData({ ...editableData });
      } else {
        setFormData({ ...newQuoteTemplate, items: [{ ...emptyItem }] });
      }
    }
  }, [quote, isOpen, newQuoteTemplate]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.clientId) {
      newErrors.clientId = 'Please select a customer';
    }

    if (!formData.issueDate) {
      newErrors.issueDate = 'Issue date is required';
    }

    if (!formData.expiryDate) {
      newErrors.expiryDate = 'Expiry date is required';
    }

    // Validate items
    const itemErrors: string[] = [];
    formData.items.forEach((item, index) => {
      if (!item.description.trim()) {
        itemErrors[index] = 'Description is required';
      } else if (item.quantity <= 0) {
        itemErrors[index] = 'Quantity must be greater than 0';
      } else if (item.unitPrice < 0) {
        itemErrors[index] = 'Unit price cannot be negative';
      }
    });

    if (itemErrors.length > 0) {
      newErrors.items = itemErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear specific field errors when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const items = [...formData.items];
    const item = { ...items[index] };

    (item as any)[name] = name === 'description' ? value : (parseFloat(value) || 0);

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));

    // Clear item errors when user makes changes
    if (errors.items && errors.items[index]) {
      const newItemErrors = [...(errors.items || [])];
      newItemErrors[index] = '';
      setErrors(prev => ({ ...prev, items: newItemErrors }));
    }
  };

  const handleProductSelect = (index: number, description: string, product?: Product) => {
    const items = [...formData.items];
    const item = { ...items[index] };

    item.description = description;
    if (product) {
      item.unitPrice = product.unitPrice;
      item.productId = product._id;
    } else {
      item.productId = undefined;
    }

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));
  };

  const addItem = () => {
    setFormData(prev => ({ ...prev, items: [...prev.items, { ...emptyItem }] }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length <= 1) return;
    const items = formData.items.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, items }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      setErrors({ general: 'Failed to save quote. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
  const selectedTaxRate = taxRates.find(t => t._id === formData.taxId)?.rate || 0;
  const taxAmount = subtotal * selectedTaxRate;
  const totalAmount = subtotal + taxAmount;

  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="3xl">
      <div className="relative bg-white">
        {/* Premium Header with Gradient */}
        <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
          <div className="absolute inset-0 opacity-20" style={{backgroundImage: "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.03\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"}}></div>
          <div className="relative px-8 py-8">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold tracking-tight">
                      {isEditing ? 'Edit Quote' : 'Create New Quote'}
                    </h1>
                    <p className="text-white/70 text-sm font-medium">
                      {isEditing ? 'Update your quote details with precision' : 'Design a professional quote for your client'}
                    </p>
                  </div>
                </div>
              </div>
              <div className="text-right space-y-1">
                <div className="text-white/60 text-xs font-medium uppercase tracking-wider">Quote Number</div>
                <div className="text-2xl font-bold text-white font-mono tracking-wide bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg border border-white/20">
                  {formData.quoteNumber || generateQuoteNumber()}
                </div>
              </div>
            </div>

            {/* Status Badge */}
            <div className="mt-6 flex items-center justify-between">
              <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-white/10 backdrop-blur-sm text-white border border-white/20">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse"></div>
                {formData.status}
              </div>
              <button
                type="button"
                onClick={onClose}
                title="Close modal"
                className="text-white/60 hover:text-white hover:bg-white/10 rounded-lg p-2 transition-all duration-200"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="h-full flex flex-col">
          <div className="flex-1 overflow-y-auto">
            <div className="p-8 space-y-10">
              {/* General Error */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-800">{errors.general}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Basic Information Section */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 pb-4 border-b border-gray-100">
                  <div className="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900">Client & Quote Details</h4>
                </div>
                <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Select
                        label="Customer *"
                        name="clientId"
                        value={formData.clientId}
                        onChange={handleChange}
                        required
                        className={`transition-all duration-200 ${errors.clientId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      >
                        <option value="" disabled>Select a client</option>
                        {clients.map(client => (
                          <option key={client._id} value={client._id}>{client.name}</option>
                        ))}
                      </Select>
                      {errors.clientId && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-sm font-medium">{errors.clientId}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Select
                        label="Status"
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                      >
                        {Object.values(QuoteStatus).map(s => (
                          <option key={s} value={s}>{s}</option>
                        ))}
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Input
                        label="Issue Date *"
                        type="date"
                        name="issueDate"
                        value={formData.issueDate}
                        onChange={handleChange}
                        required
                        className={`transition-all duration-200 ${errors.issueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      />
                      {errors.issueDate && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-sm font-medium">{errors.issueDate}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Input
                        label="Expiry Date *"
                        type="date"
                        name="expiryDate"
                        value={formData.expiryDate}
                        onChange={handleChange}
                        required
                        className={`transition-all duration-200 ${errors.expiryDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      />
                      {errors.expiryDate && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-sm font-medium">{errors.expiryDate}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Items Section */}
              <div className="space-y-6">
                <div className="flex items-center justify-between pb-4 border-b border-gray-100">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">Quote Items</h4>
                  </div>
                  <Button
                    type="button"
                    variant="primary"
                    size="sm"
                    onClick={addItem}
                    className="flex items-center gap-2 bg-gray-900 hover:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Item
                  </Button>
                </div>

                <div className="bg-white border border-gray-200 rounded-2xl shadow-lg overflow-hidden">
                  {/* Premium Table Header */}
                  <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 px-8 py-6">
                    <div className="grid grid-cols-12 gap-4 text-sm font-bold text-white uppercase tracking-wider">
                      <div className="col-span-5 flex items-center space-x-2">
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <span>Product/Description</span>
                      </div>
                      <div className="col-span-2 text-center">Quantity</div>
                      <div className="col-span-2 text-center">Unit Price</div>
                      <div className="col-span-2 text-center">Total</div>
                      <div className="col-span-1 text-center">Actions</div>
                    </div>
                  </div>

                  {/* Premium Table Body */}
                  <div className="divide-y divide-gray-50">
                    {formData.items.map((item, index) => (
                      <div key={index} className="group hover:bg-gray-50/50 transition-all duration-200">
                        <div className="px-8 py-6">
                          <div className="grid grid-cols-12 gap-6 items-center">
                            {/* Product/Description Column */}
                            <div className="col-span-5">
                              <div className="space-y-3">
                                <div className="relative">
                                  <ProductSelector
                                    value={item.description}
                                    onChange={(value, product) => handleProductSelect(index, value, product)}
                                    products={products}
                                    placeholder="Search products or enter description..."
                                  />
                                </div>
                                {errors.items && errors.items[index] && (
                                  <div className="flex items-center gap-2 text-red-600 bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg className="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p className="text-sm font-medium">{errors.items[index]}</p>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Quantity Column */}
                            <div className="col-span-2">
                              <div className="relative group">
                                <input
                                  type="number"
                                  name="quantity"
                                  value={item.quantity}
                                  onChange={(e) => handleItemChange(index, e)}
                                  min="0"
                                  step="1"
                                  placeholder="0"
                                  className="w-full px-4 py-3 text-center border border-gray-300 rounded-xl shadow-sm font-semibold text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200 hover:shadow-md"
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                  <span className="text-gray-400 text-xs font-medium">QTY</span>
                                </div>
                              </div>
                            </div>

                            {/* Unit Price Column */}
                            <div className="col-span-2">
                              <div className="relative group">
                                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                  <span className="text-gray-500 text-sm font-semibold">$</span>
                                </div>
                                <input
                                  type="number"
                                  name="unitPrice"
                                  value={item.unitPrice}
                                  onChange={(e) => handleItemChange(index, e)}
                                  min="0"
                                  step="0.01"
                                  placeholder="0.00"
                                  className="w-full pl-8 pr-4 py-3 text-center border border-gray-300 rounded-xl shadow-sm font-semibold text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200 hover:shadow-md"
                                />
                              </div>
                            </div>

                            {/* Total Column */}
                            <div className="col-span-2">
                              <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl px-4 py-3 text-center shadow-lg">
                                <span className="font-bold text-white text-lg tracking-wide">
                                  {formatCurrency(item.total)}
                                </span>
                              </div>
                            </div>

                            {/* Actions Column */}
                            <div className="col-span-1 flex justify-center">
                              <button
                                type="button"
                                onClick={() => removeItem(index)}
                                disabled={formData.items.length <= 1}
                                className="p-3 rounded-xl text-gray-400 hover:text-red-600 hover:bg-red-50 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed group-hover:opacity-100 transform hover:scale-110 shadow-sm hover:shadow-md"
                                title="Remove item"
                              >
                                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Premium Table Footer */}
                  {formData.items.length > 0 && (
                    <div className="bg-gradient-to-r from-gray-900 to-gray-800 px-8 py-4">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2 text-white/80">
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          <span className="text-sm font-medium">{formData.items.length} item{formData.items.length !== 1 ? 's' : ''} added</span>
                        </div>
                        <div className="text-white">
                          <span className="text-sm font-medium">Subtotal: </span>
                          <span className="text-lg font-bold">{formatCurrency(subtotal)}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Premium Totals Section */}
              <div className="space-y-8">
                <div className="flex items-center space-x-3 pb-4 border-b border-gray-100">
                  <div className="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                    </svg>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900">Quote Summary</h4>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Tax Controls */}
                  <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <h5 className="text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                      </svg>
                      <span>Tax Settings</span>
                    </h5>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-3">Tax Rate</label>
                        <Select
                          name="taxId"
                          value={formData.taxId}
                          onChange={handleChange}
                          className="w-full focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                        >
                          {taxRates.map(t => (
                            <option key={t._id} value={t._id}>
                              {t.name} ({(t.rate * 100).toFixed(2)}%)
                            </option>
                          ))}
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Premium Totals Display */}
                  <div className="space-y-6">
                    <h5 className="text-lg font-bold text-gray-900 flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                      </svg>
                      <span>Quote Summary</span>
                    </h5>

                    <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl border border-gray-200 p-8 space-y-6 shadow-lg">
                      {/* Subtotal */}
                      <div className="flex justify-between items-center pb-3 border-b border-gray-100">
                        <span className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Subtotal</span>
                        <span className="text-xl font-bold text-gray-900">{formatCurrency(subtotal)}</span>
                      </div>

                      {/* Tax */}
                      <div className="flex justify-between items-center pb-3 border-b border-gray-100">
                        <span className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                          {taxRates.find(t => t._id === formData.taxId)?.name || 'Tax'} ({(selectedTaxRate * 100).toFixed(2)}%)
                        </span>
                        <span className="text-xl font-bold text-gray-900">{formatCurrency(taxAmount)}</span>
                      </div>

                      {/* Total */}
                      <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl p-6 -m-2 mt-4">
                        <div className="flex justify-between items-center">
                          <span className="text-xl font-bold text-white uppercase tracking-wide">Total Amount</span>
                          <span className="text-3xl font-bold text-white">{formatCurrency(totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>

        {/* Premium Footer */}
        <div className="flex-shrink-0 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 px-8 py-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2 text-gray-600">
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">
                {isEditing ? 'Update your quote with confidence' : 'All fields marked with * are required'}
              </span>
            </div>
            <div className="flex space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-6 py-3 border-2 border-gray-300 hover:border-gray-400 transition-all duration-200"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className="min-w-[140px] px-8 py-3 bg-gray-900 hover:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="font-semibold">Saving...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="font-semibold">
                      {isEditing ? 'Update Quote' : 'Create Quote'}
                    </span>
                  </div>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default QuoteFormModal;
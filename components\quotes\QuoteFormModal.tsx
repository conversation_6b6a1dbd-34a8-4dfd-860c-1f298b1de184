import React, { useState, useEffect, useMemo } from 'react';
import { Quote, InvoiceItem, QuoteStatus, Client, Product, TaxRate } from '../../types';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Select from '../ui/Select';
import ProductSelector from '../ui/ProductSelector';

interface QuoteFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (quote: Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>) => void;
  quote: Quote | null;
  clients: Client[];
  products: Product[];
  taxRates: TaxRate[];
}

const today = new Date().toISOString().split('T')[0];
const futureDate = new Date();
futureDate.setDate(futureDate.getDate() + 30);
const expiry = futureDate.toISOString().split('T')[0];

const emptyItem: InvoiceItem = { description: '', quantity: 1, unitPrice: 0, total: 0 };
const newQuoteTemplate: Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'> = {
  clientId: '',
  issueDate: today,
  expiryDate: expiry,
  status: QuoteStatus.Draft,
  items: [emptyItem],
};

const QuoteFormModal: React.FC<QuoteFormModalProps> = ({ isOpen, onClose, onSave, quote, clients, products, taxRates }) => {
  const [formData, setFormData] = useState<Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>>({ ...newQuoteTemplate });
  const isEditing = useMemo(() => !!quote, [quote]);

  useEffect(() => {
    if (isOpen) {
      if (quote) {
        const { _id, amount, workspaceId, _creationTime, taxRate, ...editableData } = quote;
        setFormData({ ...editableData });
      } else {
        const defaultTax = taxRates.find(t => t.isDefault);
        setFormData({ ...newQuoteTemplate, items: [{ ...emptyItem }], taxId: defaultTax?._id });
      }
    }
  }, [quote, isOpen, taxRates]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const items = [...formData.items];
    const item = { ...items[index] };

    (item as any)[name] = name === 'description' ? value : (parseFloat(value) || 0);

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));
  };

  const handleProductSelect = (index: number, description: string, product?: Product) => {
    const items = [...formData.items];
    const item = { ...items[index] };

    item.description = description;
    if (product) {
      item.unitPrice = product.unitPrice;
      item.productId = product._id;
    } else {
      item.productId = undefined;
    }

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));
  };

  const addItem = () => {
    setFormData(prev => ({ ...prev, items: [...prev.items, { ...emptyItem }] }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length <= 1) return;
    const items = formData.items.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, items }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };
  
  const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
  const selectedTaxRate = taxRates.find(t => t._id === formData.taxId)?.rate || 0;
  const taxAmount = subtotal * selectedTaxRate;
  const totalAmount = subtotal + taxAmount;

  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={isEditing ? 'Edit Quote' : 'New Quote'} size="xl">
      <form onSubmit={handleSubmit}>
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Select label="Customer" name="clientId" value={formData.clientId} onChange={handleChange} required>
              <option value="" disabled>Select a client</option>
              {clients.map(client => <option key={client._id} value={client._id}>{client.name}</option>)}
            </Select>
            <Select label="Status" name="status" value={formData.status} onChange={handleChange}>
              {Object.values(QuoteStatus).map(s => <option key={s} value={s}>{s}</option>)}
            </Select>
            <Input label="Issue Date" type="date" name="issueDate" value={formData.issueDate} onChange={handleChange} required />
            <Input label="Expiry Date" type="date" name="expiryDate" value={formData.expiryDate} onChange={handleChange} required />
          </div>

          <div>
            <h4 className="text-sm font-semibold text-gray-500 mb-2">Items</h4>
            <div className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="grid grid-cols-12 gap-x-2 items-end">
                  <div className="col-span-12 sm:col-span-5">
                    <ProductSelector
                      label={index === 0 ? "Product/Description" : ""}
                      value={item.description}
                      onChange={(value, product) => handleProductSelect(index, value, product)}
                      products={products}
                      placeholder="Search products or enter description..."
                    />
                  </div>
                  <div className="col-span-4 sm:col-span-2"><Input label={index === 0 ? "Qty" : ""} type="number" name="quantity" value={item.quantity} onChange={(e) => handleItemChange(index, e)} min="0" /></div>
                  <div className="col-span-4 sm:col-span-2"><Input label={index === 0 ? "Price" : ""} type="number" name="unitPrice" value={item.unitPrice} onChange={(e) => handleItemChange(index, e)} min="0" step="0.01" /></div>
                  <div className="col-span-4 sm:col-span-2"><Input label={index === 0 ? "Total" : ""} value={formatCurrency(item.total)} readOnly disabled /></div>
                  <div className="col-span-12 sm:col-span-1 flex items-end justify-end">
                    <Button type="button" variant="ghost" size="sm" onClick={() => removeItem(index)} className="p-2 h-9 w-9 text-red-500" disabled={formData.items.length <= 1}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clipRule="evenodd" /></svg>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <Button type="button" variant="secondary" size="sm" onClick={addItem} className="mt-4">Add Item</Button>
          </div>
          
          <div className="flex justify-end">
             <div className="w-full max-w-sm space-y-3">
                 <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Subtotal</span>
                    <span className="font-medium">{formatCurrency(subtotal)}</span>
                 </div>
                 <div className="flex justify-between items-center">
                     <Select label="" name="taxId" value={formData.taxId} onChange={handleChange} className="w-40 text-sm py-1">
                        {taxRates.map(t => <option key={t._id} value={t._id}>{t.name} ({(t.rate * 100).toFixed(2)}%)</option>)}
                     </Select>
                     <span className="font-medium">{formatCurrency(taxAmount)}</span>
                 </div>
                 <div className="flex justify-between items-center border-t pt-2 mt-2">
                    <span className="text-lg font-bold">Total</span>
                    <span className="text-lg font-bold">{formatCurrency(totalAmount)}</span>
                 </div>
             </div>
          </div>

        </div>
        <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-2">
          <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
          <Button type="submit">Save Quote</Button>
        </div>
      </form>
    </Modal>
  );
};

export default QuoteFormModal;
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium Invoice Modal Styles */
.invoice-table-row {
  @apply transition-all duration-300 ease-in-out;
}

.invoice-table-row:hover {
  @apply bg-gradient-to-r from-gray-50/50 to-white shadow-lg transform scale-[1.01];
}

.invoice-input {
  @apply transition-all duration-200 ease-in-out;
}

.invoice-input:focus {
  @apply ring-2 ring-gray-900/20 border-gray-900 shadow-lg transform scale-105;
}

.invoice-total-display {
  @apply bg-gradient-to-r from-gray-900 to-gray-800 border-2 border-gray-700 shadow-lg;
}

/* Premium animations for add/remove items */
.invoice-item-enter {
  @apply opacity-0 transform scale-95 translate-y-4;
}

.invoice-item-enter-active {
  @apply opacity-100 transform scale-100 translate-y-0 transition-all duration-500 ease-out;
}

.invoice-item-exit {
  @apply opacity-100 transform scale-100 translate-y-0;
}

.invoice-item-exit-active {
  @apply opacity-0 transform scale-95 translate-y-4 transition-all duration-300 ease-in;
}

/* Premium gradient backgrounds */
.premium-gradient {
  background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #000000 100%);
}

.premium-card {
  @apply bg-white border border-gray-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02];
}

/* Enhanced focus states */
.premium-input:focus {
  @apply ring-4 ring-gray-900/10 border-gray-900 shadow-xl transform scale-105;
}

/* Smooth hover effects */
.premium-button {
  @apply transition-all duration-200 transform hover:scale-105 hover:shadow-xl;
}

/* Professional table styling */
.premium-table-header {
  background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #000000 100%);
  @apply text-white shadow-lg;
}

.premium-table-row {
  @apply hover:bg-gradient-to-r hover:from-gray-50/30 hover:to-white hover:shadow-md transition-all duration-200;
}

.invoice-item-exit {
  @apply opacity-100 transform scale-100;
}

.invoice-item-exit-active {
  @apply opacity-0 transform scale-95 transition-all duration-200 ease-in;
}